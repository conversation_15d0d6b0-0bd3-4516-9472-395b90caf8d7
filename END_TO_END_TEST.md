# End-to-End Mass Testing Documentation

## 🎯 Overview

Script `test-user-flow.js` sekarang mendukung **full end-to-end testing** yang mencakup:
1. **User Registration & Login**
2. **Assessment Submission** 
3. **Job Processing & Completion**
4. **Result Retrieval**
5. **WebSocket Real-time Notifications**

Ini adalah testing yang paling realistis untuk mengukur performa sistem secara keseluruhan.

## 🚀 Quick Start

### **Basic End-to-End Test (10 users)**
```bash
node test-user-flow.js --end-to-end --users=10
```

### **High-Performance Test (100 users)**
```bash
node test-user-flow.js -e --users=100 --high-performance
```

### **Large Scale Test (500 users)**
```bash
node test-user-flow.js -e --users=500 --hp --batch-size=20
```

## 📊 Test Flow

### **Step-by-Step Process:**
```
📦 Batch 1: Users 1-10
   Step 1: Logging in 10 users...
   Step 2: Submitting assessments...
   Step 3: Connecting WebSockets...
   
📦 Batch 2: Users 11-20
   Step 1: Logging in 10 users...
   Step 2: Submitting assessments...
   Step 3: Connecting WebSockets...
   
⏳ Step 4: Waiting for job completions...
```

### **What Gets Tested:**
- ✅ **User Registration** (create account)
- ✅ **User Login** (authenticate)
- ✅ **Profile Update** (optional, skipped in HP mode)
- ✅ **Assessment Submission** (submit RIASEC + OCEAN + VIA-IS data)
- ✅ **WebSocket Connection** (real-time notifications)
- ✅ **Job Processing** (AI analysis in background)
- ✅ **Job Completion** (wait for results)
- ✅ **Result Retrieval** (get final analysis)

## ⚙️ Configuration Options

### **Basic Options**
| Parameter | Default | Description |
|-----------|---------|-------------|
| `--users=N` | 50 | Number of users to test |
| `--batch-size=N` | 10 | Users per batch |
| `--batch-delay=N` | 3000ms | Delay between batches |
| `--user-delay=N` | 200ms | Delay between users in batch |
| `--ws-delay=N` | 300ms | Delay between WebSocket connections |
| `--job-timeout=N` | 300000ms | Job completion timeout (5 minutes) |

### **Performance Options**
| Parameter | Description |
|-----------|-------------|
| `--high-performance`, `--hp` | Enable maximum speed mode |
| `--no-websocket` | Skip WebSocket testing |
| `--no-cleanup` | Keep user accounts after test |

## 🎯 Recommended Test Scenarios

### **1. Development Testing (10-50 users)**
```bash
# Quick validation
node test-user-flow.js -e --users=10

# Medium load test
node test-user-flow.js -e --users=50 --hp
```

### **2. Load Testing (100-500 users)**
```bash
# Standard load test
node test-user-flow.js -e --users=100 --hp --batch-size=20

# High load test
node test-user-flow.js -e --users=500 --hp --batch-size=25
```

### **3. Stress Testing (1000+ users)**
```bash
# Maximum stress test
node test-user-flow.js -e --users=1000 --hp --batch-size=50 --job-timeout=600000
```

## 📈 Expected Results

### **Small Scale (10 users)**
```
📊 RESULTS SUMMARY:
   Total Users: 10
   Login Success: 10 (100.0%)
   Assessment Success: 10 (100.0%)
   WebSocket Success: 10 (100.0%)
   Job Success: 10 (100.0%)

⏱️  TIMING STATISTICS:
   Total Test Duration: 45.2s
   Average Login Time: 1200ms
   Average Assessment Time: 800ms
   Average Job Completion Time: 35.5s
```

### **Medium Scale (100 users)**
```
📊 RESULTS SUMMARY:
   Total Users: 100
   Login Success: 100 (100.0%)
   Assessment Success: 100 (100.0%)
   WebSocket Success: 100 (100.0%)
   Job Success: 95+ (95%+)

⏱️  TIMING STATISTICS:
   Total Test Duration: 180.7s
   Average Login Time: 2500ms
   Average Assessment Time: 1200ms
   Average Job Completion Time: 120.3s
```

### **Large Scale (500 users)**
```
📊 RESULTS SUMMARY:
   Total Users: 500
   Login Success: 500 (100.0%)
   Assessment Success: 490+ (98%+)
   WebSocket Success: 485+ (97%+)
   Job Success: 450+ (90%+)

⏱️  TIMING STATISTICS:
   Total Test Duration: 600.5s
   Average Login Time: 4000ms
   Average Assessment Time: 2000ms
   Average Job Completion Time: 240.8s
```

## 🔍 Bottleneck Analysis

### **Primary Bottlenecks:**
1. **Job Processing Time** - AI analysis takes 30-120 seconds per job
2. **Database Concurrent Writes** - Assessment submissions
3. **Analysis Worker Queue** - Limited worker concurrency
4. **Memory Usage** - Large number of concurrent jobs

### **Secondary Bottlenecks:**
1. **Login Process** - Password hashing + JWT generation
2. **WebSocket Connections** - Memory per connection
3. **Network I/O** - HTTP request/response cycles

## ⚡ Performance Optimization

### **High-Performance Mode Benefits:**
- ✅ **Skip Profile Updates** - Reduces database load
- ✅ **Faster Batch Processing** - Reduced delays
- ✅ **Parallel Execution** - Maximum concurrency
- ✅ **Optimized Timeouts** - Faster failure detection

### **Expected Improvements:**
- **50% faster login** process
- **30% faster** overall test duration
- **2x higher** user throughput
- **Better resource** utilization

## 🚨 System Limits

### **Current Theoretical Limits:**
- **Rate Limiting:** 2500 auth requests per 15 minutes
- **Database Connections:** 100 total (50 per service)
- **Analysis Workers:** 10 concurrent jobs (configurable)
- **Job Queue:** Unlimited (Redis-based)
- **WebSocket Connections:** Memory-limited (~1000+)

### **Recommended Limits:**
- **Development:** 10-50 users
- **Load Testing:** 100-500 users  
- **Stress Testing:** 500-1000 users
- **Production Simulation:** 100-200 users

## 🔧 Troubleshooting

### **Common Issues:**

1. **Job Timeout Errors**
   ```bash
   # Increase timeout for slow processing
   node test-user-flow.js -e --users=50 --job-timeout=600000
   ```

2. **Database Connection Errors**
   ```bash
   # Reduce batch size
   node test-user-flow.js -e --users=100 --batch-size=5
   ```

3. **Memory Issues**
   ```bash
   # Skip WebSocket testing
   node test-user-flow.js -e --users=500 --no-websocket
   ```

### **Monitoring Commands:**
```bash
# Monitor system resources
htop                    # CPU/Memory usage
netstat -an | grep :3000 # Network connections

# Monitor database
psql -c "SELECT count(*) FROM pg_stat_activity;"

# Monitor Redis queue
redis-cli monitor
```

## 📋 Progressive Testing Strategy

### **Step 1: Validation (10 users)**
```bash
node test-user-flow.js -e --users=10
```
**Goal:** Verify all components work correctly

### **Step 2: Load Testing (50-100 users)**
```bash
node test-user-flow.js -e --users=50 --hp
node test-user-flow.js -e --users=100 --hp
```
**Goal:** Identify performance characteristics

### **Step 3: Stress Testing (500+ users)**
```bash
node test-user-flow.js -e --users=500 --hp --batch-size=25
```
**Goal:** Find breaking points and limits

### **Step 4: Endurance Testing**
```bash
# Run multiple tests back-to-back
for i in {1..5}; do
  node test-user-flow.js -e --users=100 --hp
  sleep 60
done
```
**Goal:** Test system stability over time

## 🎯 Success Criteria

### **Acceptable Performance:**
- **Login Success Rate:** >95%
- **Assessment Success Rate:** >90%
- **Job Success Rate:** >85%
- **Average Job Time:** <180 seconds
- **System Stability:** No crashes or memory leaks

### **Excellent Performance:**
- **Login Success Rate:** >99%
- **Assessment Success Rate:** >95%
- **Job Success Rate:** >90%
- **Average Job Time:** <120 seconds
- **Resource Usage:** <80% CPU/Memory

## 🚀 Quick Commands Reference

```bash
# Basic tests
node test-user-flow.js -e --users=10                    # Quick test
node test-user-flow.js -e --users=50 --hp               # Medium test
node test-user-flow.js -e --users=100 --hp              # Load test

# Advanced tests  
node test-user-flow.js -e --users=500 --hp --batch-size=25    # Stress test
node test-user-flow.js -e --users=1000 --hp --job-timeout=600000  # Max test

# Debug mode
DEBUG_RESPONSE=true node test-user-flow.js -e --users=5 --hp
```
