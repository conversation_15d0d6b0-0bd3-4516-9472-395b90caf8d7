# Mass Login Test Documentation

## Overview

Script `test-user-flow.js` telah diupdate untuk mendukung testing login 250 user sekaligus dengan koneksi WebSocket. Script ini dirancang untuk menguji performa dan stabilitas sistem ATMA backend dalam menangani beban tinggi.

## Fitur Mass Login Test

### 1. **Batch Processing**
- Memproses user dalam batch untuk menghindari overwhelming server
- Default: 10 user per batch dengan delay 2 detik antar batch
- Dapat dikonfigurasi sesuai kebutuhan

### 2. **Rate Limiting Mitigation**
- Delay antar user dalam batch (default: 100ms)
- Delay antar koneksi WebSocket (default: 200ms)
- Mencegah rate limiting dari server

### 3. **WebSocket Testing**
- Otomatis menguji koneksi WebSocket untuk setiap user yang berhasil login
- Monitoring status koneksi real-time
- Graceful cleanup semua koneksi

### 4. **Comprehensive Reporting**
- Statistik lengkap success/failure rate
- Timing analysis (rata-rata waktu login dan WebSocket)
- Error categorization dan counting
- Connection monitoring

## Cara Penggunaan

### Basic Mass Login Test (250 users)
```bash
node test-user-flow.js --mass-login
```

### Custom Configuration
```bash
# Test dengan 100 user, batch size 5
node test-user-flow.js --mass-login --users=100 --batch-size=5

# Test 50 user tanpa WebSocket
node test-user-flow.js -m --users=50 --no-websocket

# Test dengan delay yang lebih besar untuk server yang lambat
node test-user-flow.js -m --users=100 --batch-delay=5000 --user-delay=500
```

### Parameter Configuration

| Parameter | Default | Deskripsi |
|-----------|---------|-----------|
| `--users=N` | 250 | Jumlah user yang akan ditest |
| `--batch-size=N` | 10 | Jumlah user per batch |
| `--batch-delay=N` | 2000 | Delay antar batch (ms) |
| `--user-delay=N` | 100 | Delay antar user dalam batch (ms) |
| `--ws-delay=N` | 200 | Delay antar koneksi WebSocket (ms) |
| `--no-websocket` | false | Skip testing WebSocket |
| `--no-cleanup` | false | Skip cleanup user setelah test |

## Potensi Masalah dan Solusi

### 1. **Rate Limiting** ✅ **SOLVED**
**Masalah**: Server memiliki rate limit yang terlalu rendah untuk mass testing

**Solusi yang diimplementasikan**:
- ✅ **Rate limits ditingkatkan untuk mendukung 1000+ user**:
  - Auth endpoints: 2500 requests per 15 menit (register + login + profile)
  - General endpoints: 5000 requests per 15 menit
  - Assessment: 1000 submissions per hour
  - Admin: 1000 requests per 15 menit
- Batch processing dengan delay (tetap dipertahankan untuk stabilitas)
- Monitoring dan error handling

### 2. **Database Connection Pool**
**Masalah**: Setiap service hanya memiliki max 25 database connections

**Solusi yang diimplementasikan**:
- Batch processing untuk mengurangi concurrent load
- Graceful error handling
- Connection reuse

### 3. **Memory Usage**
**Masalah**: 250 WebSocket connections membutuhkan memory signifikan

**Solusi yang diimplementasikan**:
- Monitoring connection count
- Automatic cleanup
- Configurable test duration

### 4. **Network Bandwidth**
**Masalah**: Bandwidth dan server processing power

**Solusi yang diimplementasikan**:
- Configurable delays
- Batch processing
- Connection status monitoring

## Monitoring dan Debugging

### 1. **Real-time Monitoring**
Script akan menampilkan:
- Progress per batch
- Success/failure rate real-time
- Connection status dari notification service
- Error categorization

### 2. **Debug Mode**
```bash
DEBUG_RESPONSE=true node test-user-flow.js --mass-login --users=10
```

### 3. **Connection Status Check**
Script otomatis mengecek status koneksi dari notification service:
```
📊 Checking notification service status...
   Total connections: 245
   Authenticated users: 245
   Unique users: 245
```

## Rekomendasi Penggunaan

### 1. **Testing Bertahap**
```bash
# Step 1: Test kecil dulu
node test-user-flow.js -m --users=10

# Step 2: Test medium
node test-user-flow.js -m --users=50

# Step 3: Test full scale
node test-user-flow.js -m --users=250
```

### 2. **Server Monitoring**
Selama test, monitor:
- CPU usage
- Memory usage
- Database connections
- Network bandwidth
- Log files untuk errors

### 3. **Konfigurasi untuk Server Lambat**
```bash
# Untuk server dengan performa terbatas
node test-user-flow.js -m --users=100 --batch-size=5 --batch-delay=5000 --user-delay=1000
```

## Expected Results

### Successful Test Output
```
🎉 MASS LOGIN TEST COMPLETED!
============================================================
📊 RESULTS SUMMARY:
   Total Users: 250
   Login Success: 248 (99.2%)
   Login Failed: 2 (0.8%)
   WebSocket Success: 245 (98.8%)
   WebSocket Failed: 3 (1.2%)

⏱️  TIMING STATISTICS:
   Total Test Duration: 127.3s
   Average Login Time: 1247ms
   Average WebSocket Time: 892ms
```

### Common Issues
1. **Rate Limiting**: Increase delays if you see many rate limit errors
2. **Database Timeouts**: Reduce batch size if database connections are exhausted
3. **WebSocket Failures**: Check notification service logs and increase WebSocket delays

## Cleanup

Script otomatis melakukan cleanup:
- Disconnect semua WebSocket connections
- Delete user accounts yang dibuat (kecuali jika `--no-cleanup` digunakan)
- Reset internal state

## Safety Features

1. **Health Check**: Mengecek semua services sebelum memulai test
2. **Graceful Failure**: Melanjutkan test meskipun ada beberapa user yang gagal
3. **Automatic Cleanup**: Membersihkan resources secara otomatis
4. **Error Categorization**: Membantu debugging dengan kategorisasi error
5. **Timeout Protection**: Mencegah hanging connections

## Kesimpulan

Script ini aman digunakan untuk testing 250 user login simultan dengan WebSocket connections. Implementasi batch processing dan delay mechanisms membantu menghindari masalah rate limiting dan resource exhaustion. Monitoring dan reporting yang comprehensive membantu dalam analisis performa sistem.
