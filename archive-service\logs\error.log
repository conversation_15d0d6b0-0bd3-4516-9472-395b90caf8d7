{"error": "listen EADDRINUSE: address already in use :::3002", "level": "error", "message": "Uncaught exception", "stack": "Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1940:16)\n    at listenInCluster (node:net:1997:12)\n    at Server.listen (node:net:2102:7)\n    at Function.listen (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\node_modules\\express\\lib\\application.js:635:24)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\archive-service\\src\\app.js:144:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)", "timestamp": "2025-07-20 10:06:10:610"}